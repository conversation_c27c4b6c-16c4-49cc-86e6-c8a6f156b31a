// API configuration
const getApiBaseUrl = (): string => {
  // In production (Docker), use relative URLs since nginx handles routing
  // In development, use localhost with port
  if (import.meta.env.PROD) {
    return '/api';
  }
  
  // For development, check if custom API URL is provided
  const customApiUrl = import.meta.env.VITE_API_URL;
  if (customApiUrl) {
    return customApiUrl;
  }
  
  // Default development URL
  return 'http://localhost:3001';
};

export const API_BASE_URL = getApiBaseUrl();

// API endpoints
export const API_ENDPOINTS = {
  LOGIN: `${API_BASE_URL}/login`,
  COMPANY_PROFILING: `${API_BASE_URL}/company_profiling`,
  HEALTH: `${API_BASE_URL}/health`,
} as const;
